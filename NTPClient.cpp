#include "NTPClient.h"

NTPClient::NTPClient() {
}

void NTPClient::Setup(MatrixPanel_I2S_DMA* d) {
  _display = d;
  udp.begin(localPort);
  Serial.println("NTP Client started");
  Serial.print("Local port: ");
  Serial.println(udp.localPort());
}

unsigned long NTPClient::sendNTPpacket(IPAddress& address) {
  Serial.println("Sending NTP packet...");
  memset(packetBuffer, 0, NTP_PACKET_SIZE);
  packetBuffer[0] = 0b11100011;
  packetBuffer[1] = 0;
  packetBuffer[2] = 6;
  packetBuffer[3] = 0xEC;
  packetBuffer[12] = 49;
  packetBuffer[13] = 0x4E;
  packetBuffer[14] = 49;
  packetBuffer[15] = 52;

  udp.beginPacket(address, 123);
  udp.write(packetBuffer, NTP_PACKET_SIZE);
  udp.endPacket();
  return 0;
}

void NTPClient::AskCurrentEpoch() {
  Serial.println("AskCurrentEpoch called");
  WiFi.hostByName(ntpServerName, timeServerIP);
  Serial.print("NTP server IP: ");
  Serial.println(timeServerIP);
  sendNTPpacket(timeServerIP);
}

unsigned long NTPClient::ReadCurrentEpoch() {
  Serial.println("ReadCurrentEpoch called");
  int cb = udp.parsePacket();
  if (!cb) {
    error_getTime = false;
    Serial.println("No packet yet");
  } else {
    error_getTime = true;
    Serial.print("Packet received, length=");
    Serial.println(cb);
    udp.read(packetBuffer, NTP_PACKET_SIZE);
    
    unsigned long highWord = word(packetBuffer[40], packetBuffer[41]);
    unsigned long lowWord = word(packetBuffer[42], packetBuffer[43]);
    unsigned long secsSince1900 = highWord << 16 | lowWord;
    Serial.print("Seconds since Jan 1 1900 = ");
    Serial.println(secsSince1900);
    
    const unsigned long seventyYears = 2208988800UL;
    lastEpoch = secsSince1900 - seventyYears;
    lastEpochTimeStamp = nextEpochTimeStamp;
    
    Serial.print("Unix time = ");
    Serial.println(lastEpoch);
    return lastEpoch;
  }
  return 0;
}

unsigned long NTPClient::GetCurrentTime() {
  unsigned long timeNow = millis();
  if (timeNow > timeToAsk || !error_getTime) {
    Serial.println("Time to ask NTP server");
    timeToAsk = timeNow + askFrequency;
    if (timeToRead == 0) {
      timeToRead = timeNow + 1000;
      AskCurrentEpoch();
      nextEpochTimeStamp = millis();
    } 
  }

  if (timeToRead > 0 && timeNow > timeToRead) {
    Serial.println("Time to read NTP response");
    ReadCurrentEpoch();
    timeToRead = 0;
  }
    
  if (lastEpoch != 0) {
    unsigned long zoneOffset = timezoneOffset * 3600;
    unsigned long elapsedMillis = millis() - lastEpochTimeStamp;
    currentTime = lastEpoch + zoneOffset + (elapsedMillis / 1000);
  }
  return currentTime;
}

byte NTPClient::GetHours() {
  int hours = (currentTime % 86400L) / 3600;
  
  if (!militaryTime) {
    if (hours == 0) hours = 12;
    if (hours > 12) hours -= 12;
  }
  return hours;
}

byte NTPClient::GetMinutes() {
  return (currentTime % 3600) / 60;
}

byte NTPClient::GetSeconds() {
  return currentTime % 60;
}

void NTPClient::PrintTime() {
  Serial.print("Time: ");
  Serial.print(GetHours());
  Serial.print(":");
  if (GetMinutes() < 10) Serial.print('0');
  Serial.print(GetMinutes());
  Serial.print(":");
  if (GetSeconds() < 10) Serial.print('0');
  Serial.println(GetSeconds());
}

void NTPClient::setTimezone(int tz) {
  timezoneOffset = tz;
  Serial.print("Timezone set to: ");
  Serial.println(tz);
}

void NTPClient::setMilitaryTime(bool military) {
  militaryTime = military;
  Serial.print("Military time: ");
  Serial.println(military ? "enabled" : "disabled");
}