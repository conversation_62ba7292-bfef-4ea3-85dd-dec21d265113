#include <Arduino.h>
#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
#include "ESP32-HUB75-MatrixPanel-I2S-DMA.h"
#include <WiFi.h>
#include <WebServer.h>
#include <SPIFFS.h>
#include "Digit.h"
#include "NTPClient.h"

// Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CLK_PIN 37

// Panel Configuration
#define PANEL_RES_X 64
#define PANEL_RES_Y 32
#define PANEL_CHAIN 1

// WiFi credentials
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

MatrixPanel_I2S_DMA* dma_display = nullptr;
WebServer server(80);

// Colors
uint16_t BLACK, WHITE, RED, GREEN, BLUE, YELLOW, CYAN, MAGENTA;

// Clock digits
Digit* digit0;
Digit* digit1;
Digit* digit2;
Digit* digit3;
Digit* digit4;
Digit* digit5;

// Clock variables
unsigned long prevEpoch;
byte prevhh;
byte prevmm;
byte prevss;

// Settings
int timezoneOffset = 0;
bool militaryTime = true;

NTPClient ntpClient;

#line 61 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void setup();
#line 95 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void loop();
#line 144 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void setupDisplay();
#line 173 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void initColors();
#line 184 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void initDigits();
#line 197 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void setupWiFi();
#line 210 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void setupWebServer();
#line 237 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void handleTime();
#line 248 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void handleGetSettings();
#line 256 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void handleSetSettings();
#line 279 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void drawWelcomeScreen();
#line 61 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/esp32_morphing_clock_wifi.ino"
void setup() {
  Serial.begin(115200);

  // Initialize SPIFFS
  if(!SPIFFS.begin(true)){
    Serial.println("SPIFFS Mount Failed");
    return;
  }

  // Initialize display
  setupDisplay();

  // Initialize colors
  initColors();

  // Initialize clock digits
  initDigits();

  // Setup WiFi
  setupWiFi();

  // Setup NTP
  ntpClient.Setup(dma_display);

  // Setup web server
  setupWebServer();

  Serial.println("ESP32 Morphing Clock WiFi Ready!");
  Serial.print("IP Address: ");
  Serial.println(WiFi.localIP());

  drawWelcomeScreen();
}

void loop() {
  server.handleClient();

  unsigned long epoch = ntpClient.GetCurrentTime();
  if (epoch != 0) ntpClient.PrintTime();

  if (epoch != prevEpoch) {
    int hh = ntpClient.GetHours();
    int mm = ntpClient.GetMinutes();
    int ss = ntpClient.GetSeconds();
    
    if (prevEpoch == 0) {
      digit0->Draw(ss % 10);
      digit1->Draw(ss / 10);
      digit2->Draw(mm % 10);
      digit3->Draw(mm / 10);
      digit4->Draw(hh % 10);
      digit5->Draw(hh / 10);
    } else {
      if (ss != prevss) { 
        int s0 = ss % 10;
        int s1 = ss / 10;
        if (s0 != digit0->Value()) digit0->Morph(s0);
        if (s1 != digit1->Value()) digit1->Morph(s1);
        prevss = ss;
      }

      if (mm != prevmm) {
        int m0 = mm % 10;
        int m1 = mm / 10;
        if (m0 != digit2->Value()) digit2->Morph(m0);
        if (m1 != digit3->Value()) digit3->Morph(m1);
        prevmm = mm;
      }
      
      if (hh != prevhh) {
        int h0 = hh % 10;
        int h1 = hh / 10;
        if (h0 != digit4->Value()) digit4->Morph(h0);
        if (h1 != digit5->Value()) digit5->Morph(h1);
        prevhh = hh;
      }
    }
    prevEpoch = epoch;
  }

  delay(10);
}

void setupDisplay() {
  HUB75_I2S_CFG mxconfig(PANEL_RES_X, PANEL_RES_Y, PANEL_CHAIN);

  mxconfig.gpio.r1 = R1_PIN;
  mxconfig.gpio.g1 = G1_PIN;
  mxconfig.gpio.b1 = B1_PIN;
  mxconfig.gpio.r2 = R2_PIN;
  mxconfig.gpio.g2 = G2_PIN;
  mxconfig.gpio.b2 = B2_PIN;

  mxconfig.gpio.a = A_PIN;
  mxconfig.gpio.b = B_PIN;
  mxconfig.gpio.c = C_PIN;
  mxconfig.gpio.d = D_PIN;
  mxconfig.gpio.e = E_PIN;

  mxconfig.gpio.lat = LAT_PIN;
  mxconfig.gpio.oe = OE_PIN;
  mxconfig.gpio.clk = CLK_PIN;

  mxconfig.clkphase = false;
  mxconfig.driver = HUB75_I2S_CFG::FM6124;

  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(90);
  dma_display->clearScreen();
}

void initColors() {
  BLACK = dma_display->color565(0, 0, 0);
  WHITE = dma_display->color565(255, 255, 255);
  RED = dma_display->color565(255, 0, 0);
  GREEN = dma_display->color565(0, 255, 0);
  BLUE = dma_display->color565(0, 0, 255);
  YELLOW = dma_display->color565(255, 255, 0);
  CYAN = dma_display->color565(0, 255, 255);
  MAGENTA = dma_display->color565(255, 0, 255);
}

void initDigits() {
  digit0 = new Digit(dma_display, 0, 63 - 1 - 9*1, 8, BLUE);
  digit1 = new Digit(dma_display, 0, 63 - 1 - 9*2, 8, BLUE);
  digit2 = new Digit(dma_display, 0, 63 - 4 - 9*3, 8, BLUE);
  digit3 = new Digit(dma_display, 0, 63 - 4 - 9*4, 8, BLUE);
  digit4 = new Digit(dma_display, 0, 63 - 7 - 9*5, 8, BLUE);
  digit5 = new Digit(dma_display, 0, 63 - 7 - 9*6, 8, BLUE);

  dma_display->fillScreen(BLACK);
  digit1->DrawColon(BLUE);
  digit3->DrawColon(BLUE);
}

void setupWiFi() {
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");

  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }

  Serial.println();
  Serial.println("WiFi connected!");
}

void setupWebServer() {
  // Control endpoints
  server.on("/time", HTTP_GET, handleTime);
  server.on("/settings", HTTP_GET, handleGetSettings);
  server.on("/settings", HTTP_POST, handleSetSettings);

  // Serve static files
  server.on("/", HTTP_GET, [](){
    File file = SPIFFS.open("/index.html", "r");
    if(file){
      server.streamFile(file, "text/html");
      file.close();
    } else {
      server.send(404, "text/plain", "index.html not found");
    }
  });

  server.serveStatic("/", SPIFFS, "/");

  server.onNotFound([](){
    server.send(404, "text/plain", "File Not Found");
  });

  server.begin();
  Serial.println("Web server started");
}

void handleTime() {
  String json = "{";
  json += "\"hours\":" + String(ntpClient.GetHours()) + ",";
  json += "\"minutes\":" + String(ntpClient.GetMinutes()) + ",";
  json += "\"seconds\":" + String(ntpClient.GetSeconds()) + ",";
  json += "\"timezone\":" + String(timezoneOffset) + ",";
  json += "\"military\":" + String(militaryTime ? "true" : "false");
  json += "}";
  server.send(200, "application/json", json);
}

void handleGetSettings() {
  String json = "{";
  json += "\"timezone\":" + String(timezoneOffset) + ",";
  json += "\"military\":" + String(militaryTime ? "true" : "false");
  json += "}";
  server.send(200, "application/json", json);
}

void handleSetSettings() {
  String body = server.arg("plain");
  
  // Simple JSON parsing for timezone and military time
  if (body.indexOf("\"timezone\":") != -1) {
    int start = body.indexOf("\"timezone\":") + 11;
    int end = body.indexOf(",", start);
    if (end == -1) end = body.indexOf("}", start);
    timezoneOffset = body.substring(start, end).toInt();
    ntpClient.setTimezone(timezoneOffset);
  }

  if (body.indexOf("\"military\":true") != -1) {
    militaryTime = true;
    ntpClient.setMilitaryTime(true);
  } else if (body.indexOf("\"military\":false") != -1) {
    militaryTime = false;
    ntpClient.setMilitaryTime(false);
  }

  server.send(200, "text/plain", "Settings updated");
}

void drawWelcomeScreen() {
  dma_display->clearScreen();
  dma_display->setTextSize(1);
  dma_display->setTextColor(WHITE);
  dma_display->setCursor(2, 5);
  dma_display->print("MORPHING");
  dma_display->setCursor(2, 15);
  dma_display->print("CLOCK");
  dma_display->setCursor(2, 25);
  dma_display->print("WiFi Ready");
}
