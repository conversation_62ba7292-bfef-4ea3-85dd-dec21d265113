#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/NTPClient.h"
#ifndef NTPCLIENT_H
#define NTPCLIENT_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiUdp.h>
#include "ESP32-HUB75-MatrixPanel-I2S-DMA.h"

class NTPClient {
public:
  NTPClient();
  void Setup(MatrixPanel_I2S_DMA* d);
  unsigned long GetCurrentTime();
  byte GetHours();
  byte GetMinutes();
  byte GetSeconds();
  void PrintTime();
  void setTimezone(int tz);
  void setMilitaryTime(bool military);
  
private:
  MatrixPanel_I2S_DMA* _display;
  unsigned long sendNTPpacket(IPAddress& address);
  void AskCurrentEpoch();
  unsigned long ReadCurrentEpoch();
  int timezoneOffset = 0;
  bool militaryTime = true;
  
  // NTP variables
  const unsigned long askFrequency = 60*60*1000;
  unsigned long timeToAsk = 0;
  unsigned long timeToRead = 0;
  unsigned long lastEpoch = 0;
  unsigned long lastEpochTimeStamp = 0;
  unsigned long nextEpochTimeStamp = 0;
  unsigned long currentTime = 0;
  
  const char* ntpServerName = "time.google.com";
  IPAddress timeServerIP;
  const int NTP_PACKET_SIZE = 48;
  byte packetBuffer[48];
  WiFiUDP udp;
  unsigned int localPort = 2390;
  bool error_getTime = false;
};

#endif