#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_morphing_clock_wifi/README.md"
# ESP32 Morphing Clock WiFi

A beautiful morphing digital clock implementation for ESP32 with 64x32 RGB matrix display and WiFi web control interface.

## 🕐 Features

- **Morphing Digital Clock**: Smooth animated transitions between digits
- **64x32 RGB Matrix Display**: High-quality LED matrix display
- **WiFi Web Control**: Configure settings from any device with a web browser
- **NTP Time Synchronization**: Automatic time updates from internet time servers
- **Timezone Support**: Configurable timezone offset (UTC-12 to UTC+12)
- **12/24 Hour Format**: Switch between standard and military time
- **Mobile-Friendly Interface**: Responsive web interface for all devices

## 🎨 Display Features

### Morphing Animation
- Smooth pixel-by-pixel transitions between different digits
- Each digit morphs uniquely based on the source and target numbers
- Optimized animation speed for visual appeal

### Visual Layout
- **Time Display**: HH:MM:SS format with animated colons
- **Color Scheme**: Blue digits with customizable colors
- **Seven-Segment Style**: Classic digital clock appearance

## 🔧 Hardware Requirements

### ESP32 Development Board
- ESP32-S3 or similar (tested with ESP32-WROOM-32D)
- Minimum 4MB flash memory for SPIFFS

### 64x32 RGB Matrix Panel
- HUB75 interface RGB LED matrix panel
- 64x32 pixel resolution
- P4 or P5 pitch recommended

### Connections (ESP32-S3 to HUB75)

```
RGB Data Pins:
R1 -> GPIO 47    G1 -> GPIO 1     B1 -> GPIO 48
R2 -> GPIO 45    G2 -> GPIO 2     B2 -> GPIO 0

Address Pins:
A -> GPIO 35     B -> GPIO 41     C -> GPIO 36
D -> GPIO 40     E -> GPIO 42

Control Pins:
LAT -> GPIO 39   OE -> GPIO 38    CLK -> GPIO 37
```

**⚠️ Important**: GPIO 0 is the BOOT pin and must stay HIGH during boot.

## 💻 Software Requirements

### Arduino IDE Setup
1. Install Arduino IDE (1.8.19 or later)
2. Add ESP32 board support:
   - Go to File → Preferences
   - Add to Additional Board Manager URLs: `https://dl.espressif.com/dl/package_esp32_index.json`
   - Go to Tools → Board → Boards Manager
   - Search for "esp32" and install "ESP32 by Espressif Systems"

### Required Libraries
Install these libraries through Arduino IDE Library Manager:

1. **ESP32-HUB75-MatrixPanel-I2S-DMA**
   - Search for "ESP32 HUB75 Matrix Panel DMA"
   - Or install from: https://github.com/mrfaptastic/ESP32-HUB75-MatrixPanel-I2S-DMA

2. **ESP32 Core Libraries** (included with ESP32 board package):
   - WiFi
   - WebServer
   - SPIFFS
   - WiFiUdp

## 🚀 Installation

### 1. Hardware Setup
- Connect the ESP32 to the RGB matrix panel according to the pin mapping above
- Ensure stable 5V power supply for the matrix panel (3-5A recommended)
- Connect ESP32 to computer via USB

### 2. Software Setup
1. Download or clone this project
2. Open `esp32_morphing_clock_wifi.ino` in Arduino IDE
3. **Configure WiFi credentials** in the main .ino file:
   ```cpp
   const char* ssid = "YOUR_WIFI_SSID";
   const char* password = "YOUR_WIFI_PASSWORD";
   ```

### 3. Upload Files
1. **Upload SPIFFS data**:
   - Install ESP32 SPIFFS plugin for Arduino IDE
   - Select Tools → ESP32 Sketch Data Upload
   - Wait for upload to complete

2. **Upload sketch**:
   - Select your ESP32 board (e.g., "ESP32 Dev Module")
   - Set partition scheme to "Default 4MB with spiffs"
   - Click Upload

### 4. First Run
1. Open Serial Monitor (115200 baud)
2. Reset ESP32 and wait for WiFi connection
3. Note the IP address displayed in Serial Monitor
4. The matrix will show "MORPHING CLOCK WiFi Ready"

## 🌐 Usage

### Web Interface
1. Connect your phone/computer to the same WiFi network
2. Open web browser and navigate to the ESP32's IP address
3. You'll see the morphing clock control interface

### Clock Settings
- **Timezone**: Select your timezone offset from UTC (-12 to +12)
- **Time Format**: Choose between 12-hour and 24-hour (military) format
- **Real-time Updates**: Settings apply immediately to the display

### Web Interface Features
- **Live Time Display**: Shows current time from the ESP32
- **Settings Panel**: Configure timezone and time format
- **Connection Status**: Visual feedback on ESP32 connectivity
- **Clock Preview**: See how the time will appear on the matrix

## 🌐 Web API Endpoints

The ESP32 provides these HTTP endpoints:

- `GET /time` - Get current time (JSON)
- `GET /settings` - Get current settings (JSON)
- `POST /settings` - Update settings (JSON)

### Time Response Format
```json
{
  "hours": 14,
  "minutes": 30,
  "seconds": 45,
  "timezone": 8,
  "military": true
}
```

### Settings Format
```json
{
  "timezone": 8,
  "military": true
}
```

## 🔧 Troubleshooting

### Common Issues

**Matrix display not working:**
- Check all pin connections
- Verify power supply (5V, sufficient amperage)
- Ensure GPIO 0 is not pulled LOW during boot

**WiFi connection fails:**
- Double-check SSID and password in code
- Ensure ESP32 is within WiFi range
- Try different WiFi network (avoid enterprise networks)

**Web interface not loading:**
- Verify SPIFFS upload was successful
- Check Serial Monitor for IP address
- Try accessing from different device/browser

**Time not updating:**
- Check internet connectivity
- Verify NTP server accessibility
- Monitor Serial output for NTP errors

**Morphing animation too fast/slow:**
- Adjust `animSpeed` constant in Digit.cpp
- Increase value for slower animation
- Decrease value for faster animation

### Serial Monitor Output
The Serial Monitor provides helpful debugging information:
- WiFi connection status
- IP address assignment
- NTP synchronization status
- Time updates and morphing events

## 🎯 Customization

### Adjusting Animation Speed
Edit the `animSpeed` variable in `Digit.cpp`:
```cpp
int animSpeed = 30;  // Increase for slower, decrease for faster
```

### Changing Colors
Modify color definitions in the main .ino file:
```cpp
void initColors() {
  // Change BLUE to any color you prefer
  digit0 = new Digit(dma_display, 0, 63 - 1 - 9*1, 8, GREEN);
  // ... etc
}
```

### Time Display Format
The morphing clock displays time in HH:MM:SS format with:
- Hours: 00-23 (24-hour) or 01-12 (12-hour)
- Minutes: 00-59
- Seconds: 00-59
- Animated colons between segments

### NTP Server Configuration
Change the NTP server in `NTPClient.cpp`:
```cpp
const char* ntpServerName = "pool.ntp.org";  // or any other NTP server
```

## 📁 Project Structure

```
esp32_morphing_clock_wifi/
├── esp32_morphing_clock_wifi.ino  # Main Arduino sketch
├── Digit.h                        # Digit class header
├── Digit.cpp                      # Digit morphing implementation
├── NTPClient.h                    # NTP client header
├── NTPClient.cpp                  # NTP client implementation
└── data/
    └── index.html                 # Web interface
```

## 🎨 Technical Details

### Morphing Algorithm
Each digit transition is handled by specific morphing functions:
- `Morph0()` to `Morph9()` - Handle transitions to each digit
- Pixel-by-pixel animation creates smooth visual effects
- Animation speed controlled by delay between frames

### Seven-Segment Display
- Uses classic seven-segment layout (segments A-G)
- Each digit stored as bit pattern for segment activation
- Morphing animates between different segment patterns

### Color System
- 16-bit RGB565 color format
- Customizable colors for digits and background
- Support for multiple color schemes

## 🔄 Comparison with Snake Game Version

This morphing clock shares the same hardware infrastructure as the ESP32 Snake WiFi game:

### Similarities
- Same ESP32 and RGB matrix hardware
- Identical WiFi web server setup
- Similar pin configuration and display setup
- Same SPIFFS file system usage

### Differences
- **Display Purpose**: Time display vs. game graphics
- **Animation Type**: Digit morphing vs. sprite movement
- **User Interaction**: Settings configuration vs. game controls
- **Update Frequency**: Time-based vs. game loop-based

## 📄 License

This project is open source. Feel free to modify and distribute according to your needs.

## 🎉 Credits

Based on the original Morphing Clock by Hari Wiguna and inspired by:
- ESP32 Snake WiFi implementation
- PxMatrix library for RGB displays
- ESP32 web server examples

## 🚀 Future Enhancements

### Possible Additions
- **Weather Display**: Show temperature and weather icons
- **Multiple Timezones**: Display multiple world clocks
- **Alarm Function**: Set alarms with visual/audio alerts
- **Custom Animations**: Different morphing styles and effects
- **Color Themes**: Multiple color schemes and gradients
- **Date Display**: Show current date alongside time

### Hardware Expansions
- **Larger Displays**: Support for 64x64 or larger matrices
- **Audio Output**: Speaker for alarm sounds
- **Sensors**: Light sensor for automatic brightness
- **Buttons**: Physical controls for settings

Enjoy your WiFi-controlled ESP32 Morphing Clock! 🕐✨