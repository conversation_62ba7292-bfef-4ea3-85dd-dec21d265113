<!DOCTYPE html>
<html>
<head>
    <title>ESP32 Morphing Clock Control</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background-color: #121212;
            color: white;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            user-select: none;
        }

        .clock-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .time-display {
            font-size: 48px;
            font-weight: bold;
            color: #00aaff;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .date-display {
            font-size: 18px;
            color: #aaa;
            margin-bottom: 20px;
        }

        .settings-panel {
            background-color: #333;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            width: 350px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-group label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-size: 14px;
        }

        .setting-group select,
        .setting-group input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            background-color: #555;
            color: white;
            font-size: 14px;
        }

        .setting-group select:focus,
        .setting-group input:focus {
            outline: 2px solid #00aaff;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            transform: scale(1.2);
        }

        .btn {
            background-color: #00aaff;
            border: none;
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 8px;
            transition: background-color 0.3s;
            margin: 5px;
        }

        .btn:hover {
            background-color: #0088cc;
        }

        .btn:active {
            background-color: #006699;
        }

        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .connected {
            background-color: #004400;
            color: #00ff00;
        }

        .disconnected {
            background-color: #440000;
            color: #ff4444;
        }

        .clock-preview {
            background-color: #000;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .digit-display {
            font-family: 'Courier New', monospace;
            font-size: 36px;
            color: #00aaff;
            letter-spacing: 8px;
        }

        .info-panel {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-width: 350px;
        }

        .info-panel h3 {
            margin-top: 0;
            color: #00aaff;
        }

        .info-panel p {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .timezone-info {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🕐 ESP32 Morphing Clock</h1>

    <div class="clock-info">
        <div class="time-display" id="current-time">--:--:--</div>
        <div class="date-display" id="current-date">Loading...</div>
    </div>

    <div class="clock-preview">
        <div class="digit-display" id="clock-preview">88:88:88</div>
        <div class="timezone-info" id="timezone-info">Timezone: UTC+0</div>
    </div>

    <div class="settings-panel">
        <h3>⚙️ Clock Settings</h3>
        
        <div class="setting-group">
            <label for="timezone">Timezone Offset (hours from UTC):</label>
            <select id="timezone">
                <option value="-12">UTC-12 (Baker Island)</option>
                <option value="-11">UTC-11 (American Samoa)</option>
                <option value="-10">UTC-10 (Hawaii)</option>
                <option value="-9">UTC-9 (Alaska)</option>
                <option value="-8">UTC-8 (Pacific Time)</option>
                <option value="-7">UTC-7 (Mountain Time)</option>
                <option value="-6">UTC-6 (Central Time)</option>
                <option value="-5">UTC-5 (Eastern Time)</option>
                <option value="-4">UTC-4 (Atlantic Time)</option>
                <option value="-3">UTC-3 (Argentina)</option>
                <option value="-2">UTC-2 (Mid-Atlantic)</option>
                <option value="-1">UTC-1 (Azores)</option>
                <option value="0" selected>UTC+0 (Greenwich Mean Time)</option>
                <option value="1">UTC+1 (Central European Time)</option>
                <option value="2">UTC+2 (Eastern European Time)</option>
                <option value="3">UTC+3 (Moscow Time)</option>
                <option value="4">UTC+4 (Gulf Time)</option>
                <option value="5">UTC+5 (Pakistan Time)</option>
                <option value="6">UTC+6 (Bangladesh Time)</option>
                <option value="7">UTC+7 (Indochina Time)</option>
                <option value="8">UTC+8 (China Standard Time)</option>
                <option value="9">UTC+9 (Japan Standard Time)</option>
                <option value="10">UTC+10 (Australian Eastern Time)</option>
                <option value="11">UTC+11 (Solomon Islands Time)</option>
                <option value="12">UTC+12 (New Zealand Time)</option>
            </select>
        </div>

        <div class="setting-group">
            <div class="checkbox-group">
                <input type="checkbox" id="military-time" checked>
                <label for="military-time">24-Hour Format (Military Time)</label>
            </div>
        </div>

        <button class="btn" onclick="updateSettings()">Apply Settings</button>
    </div>

    <div class="connection-status disconnected" id="status">
        Connecting...
    </div>

    <div class="info-panel">
        <h3>📋 About Morphing Clock</h3>
        <p><strong>Features:</strong></p>
        <p>• Smooth morphing digit animations</p>
        <p>• WiFi-based time synchronization</p>
        <p>• Customizable timezone settings</p>
        <p>• 12/24 hour format support</p>
        <p>• Real-time web control interface</p>
        
        <p><strong>How it works:</strong></p>
        <p>The clock displays time on a 64x32 RGB LED matrix with smooth morphing animations between digits. Time is synchronized via NTP servers over WiFi.</p>
    </div>

    <script>
        let currentSettings = {
            timezone: 0,
            military: true
        };

        // Update time display
        function updateTimeDisplay(hours, minutes, seconds) {
            const timeStr = 
                String(hours).padStart(2, '0') + ':' + 
                String(minutes).padStart(2, '0') + ':' + 
                String(seconds).padStart(2, '0');
            
            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('clock-preview').textContent = timeStr;
            
            // Update date
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            document.getElementById('current-date').textContent = now.toLocaleDateString('en-US', options);
        }

        // Get time from ESP32
        async function getTime() {
            try {
                const response = await fetch('/time');
                if (response.ok) {
                    const data = await response.json();
                    updateTimeDisplay(data.hours, data.minutes, data.seconds);
                    
                    // Update timezone info
                    const tzOffset = data.timezone;
                    const tzStr = tzOffset >= 0 ? `UTC+${tzOffset}` : `UTC${tzOffset}`;
                    document.getElementById('timezone-info').textContent = `Timezone: ${tzStr}`;
                    
                    document.getElementById('status').className = 'connection-status connected';
                    document.getElementById('status').textContent = 'Connected to ESP32';
                } else {
                    throw new Error('Time fetch failed');
                }
            } catch (error) {
                document.getElementById('status').className = 'connection-status disconnected';
                document.getElementById('status').textContent = 'Connection Error';
                console.error('Error:', error);
            }
        }

        // Get settings from ESP32
        async function getSettings() {
            try {
                const response = await fetch('/settings');
                if (response.ok) {
                    const data = await response.json();
                    currentSettings = data;
                    
                    // Update UI
                    document.getElementById('timezone').value = data.timezone;
                    document.getElementById('military-time').checked = data.military;
                    
                    document.getElementById('status').className = 'connection-status connected';
                    document.getElementById('status').textContent = 'Connected to ESP32';
                } else {
                    throw new Error('Settings fetch failed');
                }
            } catch (error) {
                document.getElementById('status').className = 'connection-status disconnected';
                document.getElementById('status').textContent = 'Connection Error';
                console.error('Error:', error);
            }
        }

        // Update settings on ESP32
        async function updateSettings() {
            const timezone = parseInt(document.getElementById('timezone').value);
            const military = document.getElementById('military-time').checked;
            
            const settings = {
                timezone: timezone,
                military: military
            };

            try {
                const response = await fetch('/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                });

                if (response.ok) {
                    currentSettings = settings;
                    document.getElementById('status').className = 'connection-status connected';
                    document.getElementById('status').textContent = 'Settings Updated Successfully';
                    
                    // Update timezone info immediately
                    const tzStr = timezone >= 0 ? `UTC+${timezone}` : `UTC${timezone}`;
                    document.getElementById('timezone-info').textContent = `Timezone: ${tzStr}`;
                } else {
                    throw new Error('Settings update failed');
                }
            } catch (error) {
                document.getElementById('status').className = 'connection-status disconnected';
                document.getElementById('status').textContent = 'Settings Update Failed';
                console.error('Error:', error);
            }
        }

        // Update time every second
        setInterval(getTime, 1000);

        // Get settings every 10 seconds
        setInterval(getSettings, 10000);

        // Initial load
        getTime();
        getSettings();
    </script>
</body>
</html>